<?php

/**
 * 游戏数据库连接和表结构测试脚本
 * 
 * 这个脚本直接测试游戏世界数据库的连接和表结构
 * 不依赖 PHPUnit，可以直接运行
 */

require_once __DIR__ . '/vendor/autoload.php';

// 启动 Laravel 应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;
use Exception;

echo "\n" . str_repeat("=", 80);
echo "\n🎮 游戏世界数据库连接和表结构测试";
echo "\n" . str_repeat("=", 80);

/**
 * 测试数据库连接
 */
function testDatabaseConnection($connectionName, $description) {
    echo "\n\n📡 测试 {$description} 连接...";
    
    try {
        // 测试基本连接
        $pdo = DB::connection($connectionName)->getPdo();
        echo "\n✅ {$description} PDO 连接成功";
        
        // 测试简单查询
        $result = DB::connection($connectionName)->select('SELECT 1 as test');
        echo "\n✅ {$description} 查询测试成功: " . $result[0]->test;
        
        // 获取数据库版本
        $version = DB::connection($connectionName)->select('SELECT @@VERSION as version')[0]->version;
        echo "\n📊 {$description} 版本: " . substr($version, 0, 60) . "...";
        
        // 获取数据库配置信息
        $config = config("database.connections.{$connectionName}");
        echo "\n🔧 {$description} 配置:";
        echo "\n   主机: " . $config['host'];
        echo "\n   端口: " . $config['port'];
        echo "\n   数据库: " . $config['database'];
        echo "\n   用户名: " . $config['username'];
        
        return true;
        
    } catch (Exception $e) {
        echo "\n❌ {$description} 连接失败: " . $e->getMessage();
        return false;
    }
}

/**
 * 获取数据库表列表
 */
function getDatabaseTables($connectionName, $description) {
    echo "\n\n🗂️ 获取 {$description} 表结构...";
    
    try {
        $tables = DB::connection($connectionName)->select("
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        ");
        
        $tableNames = array_map(function($table) {
            return $table->TABLE_NAME;
        }, $tables);
        
        echo "\n📋 {$description} 数据库表 (" . count($tableNames) . " 个):";
        
        // 定义重要的表
        $importantTables = [
            'user_data', 'characters', 'clan_data', 'ally_data', 'castle', 
            'user_account', 'user_auth', 'user_info', 'pledge_ext'
        ];
        
        foreach ($tableNames as $table) {
            $isImportant = in_array($table, $importantTables) ? ' ⭐' : '';
            echo "\n   - " . $table . $isImportant;
        }
        
        // 检查重要表是否存在
        $foundImportant = array_intersect($tableNames, $importantTables);
        if (!empty($foundImportant)) {
            echo "\n✅ 找到重要表: " . implode(', ', $foundImportant);
        }
        
        return $tableNames;
        
    } catch (Exception $e) {
        echo "\n❌ 获取 {$description} 表结构失败: " . $e->getMessage();
        return [];
    }
}

/**
 * 测试表数据量
 */
function testTableDataCount($connectionName, $tableName, $description) {
    try {
        $count = DB::connection($connectionName)->table($tableName)->count();
        echo "\n📈 {$description} ({$tableName}): {$count} 条记录";
        return $count;
    } catch (Exception $e) {
        echo "\n❌ {$description} ({$tableName}): 查询失败 - " . $e->getMessage();
        return 0;
    }
}

/**
 * 测试表结构详情
 */
function getTableStructure($connectionName, $tableName, $description) {
    try {
        $columns = DB::connection($connectionName)->select("
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = ?
            ORDER BY ORDINAL_POSITION
        ", [$tableName]);
        
        echo "\n📋 {$description} ({$tableName}) 表结构:";
        foreach ($columns as $column) {
            $nullable = $column->IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL';
            $default = $column->COLUMN_DEFAULT ? " DEFAULT: {$column->COLUMN_DEFAULT}" : '';
            echo "\n   - {$column->COLUMN_NAME} ({$column->DATA_TYPE}) {$nullable}{$default}";
        }
        
        return $columns;
        
    } catch (Exception $e) {
        echo "\n❌ 获取 {$description} ({$tableName}) 表结构失败: " . $e->getMessage();
        return [];
    }
}

/**
 * 测试查询性能
 */
function testQueryPerformance($connectionName, $query, $description) {
    try {
        $startTime = microtime(true);
        $result = DB::connection($connectionName)->select($query);
        $endTime = microtime(true);
        
        $duration = ($endTime - $startTime) * 1000; // 转换为毫秒
        echo "\n⏱️ {$description}: " . round($duration, 2) . " ms";
        
        return $duration;
        
    } catch (Exception $e) {
        echo "\n❌ {$description} 性能测试失败: " . $e->getMessage();
        return -1;
    }
}

// 开始测试
echo "\n\n🚀 开始测试...";

// 1. 测试 lin2world 数据库连接
$lin2worldConnected = testDatabaseConnection('lin2world', 'lin2world (游戏世界数据库)');

// 2. 测试 lin2db 数据库连接  
$lin2dbConnected = testDatabaseConnection('lin2db', 'lin2db (用户账户数据库)');

// 3. 如果连接成功，获取表结构
if ($lin2worldConnected) {
    $lin2worldTables = getDatabaseTables('lin2world', 'lin2world');
    
    // 测试重要表的数据量
    echo "\n\n📊 lin2world 重要表数据统计:";
    $importantTables = ['user_data', 'clan_data', 'castle'];
    foreach ($importantTables as $table) {
        if (in_array($table, $lin2worldTables)) {
            testTableDataCount('lin2world', $table, '');
            
            // 获取表结构详情
            if ($table === 'user_data') {
                getTableStructure('lin2world', $table, '角色数据表');
            }
        }
    }
}

if ($lin2dbConnected) {
    $lin2dbTables = getDatabaseTables('lin2db', 'lin2db');
    
    // 测试重要表的数据量
    echo "\n\n📊 lin2db 重要表数据统计:";
    $importantTables = ['user_account'];
    foreach ($importantTables as $table) {
        if (in_array($table, $lin2dbTables)) {
            testTableDataCount('lin2db', $table, '');
            
            // 获取表结构详情
            if ($table === 'user_account') {
                getTableStructure('lin2db', $table, '用户账户表');
            }
        }
    }
}

// 4. 性能测试
if ($lin2worldConnected || $lin2dbConnected) {
    echo "\n\n⚡ 性能测试:";
    
    if ($lin2worldConnected) {
        testQueryPerformance('lin2world', 'SELECT 1', 'lin2world 简单查询');
        if (in_array('user_data', $lin2worldTables ?? [])) {
            testQueryPerformance('lin2world', 'SELECT COUNT(*) FROM user_data', 'lin2world 计数查询');
        }
    }
    
    if ($lin2dbConnected) {
        testQueryPerformance('lin2db', 'SELECT 1', 'lin2db 简单查询');
        if (in_array('user_account', $lin2dbTables ?? [])) {
            testQueryPerformance('lin2db', 'SELECT COUNT(*) FROM user_account', 'lin2db 计数查询');
        }
    }
}

// 5. 总结
echo "\n\n" . str_repeat("=", 80);
echo "\n📋 测试总结:";
echo "\n" . str_repeat("-", 40);

if ($lin2worldConnected) {
    echo "\n✅ lin2world 数据库连接正常";
    echo "\n   表数量: " . count($lin2worldTables ?? []);
} else {
    echo "\n❌ lin2world 数据库连接失败";
}

if ($lin2dbConnected) {
    echo "\n✅ lin2db 数据库连接正常";
    echo "\n   表数量: " . count($lin2dbTables ?? []);
} else {
    echo "\n❌ lin2db 数据库连接失败";
}

echo "\n\n💡 建议:";
if ($lin2worldConnected && $lin2dbConnected) {
    echo "\n✅ 数据库连接正常，可以启用游戏统计功能";
    echo "\n🔧 下一步: 在 config/app.php 中启用统计功能";
    echo "\n   - 'enable_top_pvp' => true";
    echo "\n   - 'enable_clans' => true";
    echo "\n   - 'enable_castles' => true";
    echo "\n   - 'enable_online' => true";
} else {
    echo "\n⚠️ 数据库连接存在问题，请检查配置";
    echo "\n🔧 检查项目:";
    echo "\n   - .env 文件中的数据库配置";
    echo "\n   - 网络连接到游戏服务器";
    echo "\n   - SQL Server 驱动是否正确安装";
}

echo "\n\n" . str_repeat("=", 80);
echo "\n🎮 测试完成";
echo "\n" . str_repeat("=", 80);
echo "\n";
