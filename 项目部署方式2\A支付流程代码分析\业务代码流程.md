# XXTT2 支付系统业务代码流程分析

## 🎯 文档概述

本文档详细分析XXTT2项目的支付系统业务代码流程，包括架构设计、核心逻辑、安全机制等。

---

## 📋 支付系统架构概览

### 🏗️ 整体架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端支付页面   │    │   支付控制器     │    │   支付服务层     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • 登录用户页面   │◄──►│ • DonateController│◄──►│ • FreeKassaService│
│ • 未登录页面     │    │ • 订单创建       │    │ • PayPalService  │
│ • 支付方式选择   │    │ • 回调处理       │    │ • StripeService  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                ↓
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   订单模型       │    │   支付处理服务   │    │   数据库层       │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ • DonateOrder   │◄──►│ • PaymentProcessing│◄──►│ • MySQL (CMS)   │
│ • 订单状态管理   │    │ • 奖励系统       │    │ • SQL Server    │
│ • 用户关联       │    │ • 推荐系统       │    │ • 游戏数据库     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🔄 支付流程时序图

```
用户 → 前端页面 → 控制器 → 支付服务 → 第三方支付 → 回调处理 → 订单完成
 │        │        │        │         │           │         │
 │   选择支付   验证数据   创建订单   跳转支付   支付完成   更新状态   充值到账
```

---

## 🎮 核心业务流程

### 1️⃣ 支付入口分析

#### 已登录用户流程
**路由**: `GET /pay` → `DonateController::auth_page()`
**视图**: `template::dashboard.pay`

```php
// 已登录用户充值处理
public function createOrder(Request $request)
{
    if (!Auth::check()) {
        return response()->json(['message' => '需要登录'], 401);
    }

    $validated = $request->validate([
        'pay_select' => 'required|string',
        'count' => 'required|integer|min:1',
    ]);

    $paymentMethod = $validated['pay_select'];
    $count = $validated['count'];
    $user = Auth::user();

    $payment_id = "{$user->id}_" . time();

    return $this->processPayment($paymentMethod, $payment_id, $count, null, $user->id, null);
}
```

#### 未登录用户流程
**路由**: `GET /donate` → `DonateController::index()`
**视图**: `template::pages.donate`

```php
// 未登录用户充值处理
public function store(Request $request)
{
    $validated = $request->validate([
        'server' => 'required|string',
        'pay_select' => 'required|string',
        'char_name' => 'required|string|min:3',
        'count' => 'required|integer|min:1',
    ]);

    // 验证角色存在性
    try {
        $char_id = ModelFactory::l2jModel(config('app.l2server_version'))
            ->getCharIdByCharName($char_name, $selected_server)
            ->obj_Id;
    } catch (\Exception $e) {
        return redirect()->back()->with('char_name', '角色不存在');
    }

    // 处理支付逻辑...
}
```

### 2️⃣ 支付方式处理

#### 支持的支付平台
```php
switch ($paymentMethod) {
    case 'freekassa':
        $success_url = $this->freeKassaService->createPayment($payment_id, $count);
        break;
    case 'morune':
        $success_url = $this->moruneService->createPayment($payment_id, $count);
        break;
    case 'primepayments':
        $success_url = $this->primePaymentsService->createPayment($payment_id, $count);
        break;
    case 'enot':
        $success_url = $this->enotService->createPayment($payment_id, $count);
        break;
    case 'paypal':
        $success_url = $this->paypalService->createPayment($payment_id, $count);
        break;
    case 'stripe':
        $success_url = $this->stripeService->createPayment($payment_id, $count);
        break;
    case 'midtrans':
        $success_url = $this->midTransService->createPayment($payment_id, $count);
        break;
    default:
        return redirect()->back()->withErrors(['payment_error' => '不支持的支付方式']);
}
```

#### 订单创建逻辑
```php
if ($success_url) {
    $payOrder = new DonateOrder;
    $payOrder->payment_id = $payment_id;
    $payOrder->user_id = $user_id ?? $char_id;
    $payOrder->count = $count;
    $payOrder->server_id = $selected_server;
    $payOrder->pay_system = $paymentMethod;
    $payOrder->save();

    return redirect($success_url);
}
```

### 3️⃣ 支付服务实现

#### FreeKassa支付服务示例
```php
public function createPayment($payment_id, $count)
{
    $currency = config('app.pay_system.freekassa_currency');
    $coin_price = config('app.pay_system.coin_price')[$currency];
    $sum = (int)ceil($count * $coin_price);

    $fk_id = config('app.pay_system.freekassa_project_id');
    $fk_secret = config('app.pay_system.freekassa_secret_key');

    $oa = $sum;
    $o = config('app.pay_system.freekassa_desc') . $count . ' ' . config('app.custom_config.donate_coin_name');
    $sign = md5($fk_id . ':' . $oa . ':' . $fk_secret . ':' . $currency . ':' . $o);

    return "https://pay.freekassa.com/?m={$fk_id}&oa={$oa}&o={$o}&currency={$currency}&us_payment_id={$payment_id}&s={$sign}";
}
```

#### PayPal支付服务示例
```php
public function createPayment($payment_id, $count)
{
    $currency = config('app.pay_system.paypal_currency');
    $coin_price = config('app.pay_system.coin_price')[$currency];
    $sum = (int)ceil($count * $coin_price);

    $paymentData = [
        'intent' => 'CAPTURE',
        'application_context' => [
            'return_url' => route('donate'),
            'cancel_url' => route('donate'),
        ],
        'purchase_units' => [
            [
                'reference_id' => $payment_id,
                'amount' => [
                    'currency_code' => $currency,
                    'value' => $sum,
                ],
                'custom_id' => $payment_id,
            ],
        ],
    ];

    $response = $this->paypal->createOrder($paymentData);
    return $response['links'][1]['href'] ?? null;
}
```

---

## 🔄 支付回调处理

### 1️⃣ 回调路由配置
```php
// routes/web.php
Route::post('/callback/freekassa', [DonateController::class, 'callbackFreekassa']);
Route::post('/callback/enot', [DonateController::class, 'callbackEnot']);
Route::post('/callback/morune', [DonateController::class, 'callbackMorune']);
Route::post('/callback/primepayments', [DonateController::class, 'callbackPrimePayments']);
Route::post('/callback/paypal', [DonateController::class, 'callbackPaypal']);
Route::post('/callback/stripe', [DonateController::class, 'callbackStripe']);
Route::post('/callback/midtrans', [DonateController::class, 'callbackMidTrans']);
```

### 2️⃣ 回调验证机制

#### FreeKassa回调验证
```php
public function handleWebhook(Request $request)
{
    $merchant_id = config('app.pay_system.freekassa_project_id');
    $secret_key_2 = config('app.pay_system.freekassa_secret_key_2');

    $amount = $request->input('AMOUNT');
    $merchant_order_id = $request->input('MERCHANT_ORDER_ID');
    $received_sign = $request->input('SIGN');

    $sign = md5($merchant_id . ':' . $amount . ':' . $secret_key_2 . ':' . $merchant_order_id);

    if ($sign !== $received_sign) {
        Log::error('FreeKassa: 签名验证失败');
        return response('Invalid signature', 403);
    }

    // 处理支付成功逻辑
    $this->paymentProcessingService->processPayment($merchant_order_id);
    return response('OK', 200);
}
```

#### Stripe回调验证
```php
public function handleWebhook(Request $request)
{
    $endpointSecret = config('app.pay_system.stripe_webhook_secret');
    $payload = $request->getContent();
    $signature = $request->header('Stripe-Signature');

    $event = Webhook::constructEvent($payload, $signature, $endpointSecret);

    if ($event->type === 'payment_intent.succeeded') {
        $paymentIntent = $event->data->object;
        $metadata = $paymentIntent->metadata;
        $paymentId = $metadata->payment_id ?? null;

        if (!$paymentId) {
            return response('Missing metadata payment_id', 400);
        }

        $this->paymentProcessingService->processPayment($paymentId);
        Log::info('Payment processed successfully', ['payment_id' => $paymentId]);
    }

    return response('OK', 200);
}
```

---

## ⚙️ 支付处理核心服务

### PaymentProcessingService 核心逻辑

#### 主处理方法
```php
public function processPayment($payment_id)
{
    try {
        DB::transaction(function () use ($payment_id) {
            $payOrder = DonateOrder::where('payment_id', $payment_id)
                ->where('status', 0)
                ->first();

            if (!$payOrder) {
                throw new \Exception('订单不存在或已处理');
            }

            if ($payOrder->server_id) {
                // 未登录用户 - 直接充值到游戏角色
                $this->processCharacterPayment($payOrder->user_id, $payOrder->count, $payOrder->server_id);
            } else {
                // 已登录用户 - 充值到用户余额
                $this->processUserPayment($payOrder->user_id, $payOrder->count);
            }

            $payOrder->update(['status' => 1]);
        });
    } catch (\Exception $e) {
        Log::error("支付处理失败: {$e->getMessage()}");
        throw $e;
    }
}
```

#### 用户充值处理
```php
private function processUserPayment($user_id, $count)
{
    $user = User::findOrFail($user_id);

    // 百分比奖励系统
    if (config('app.bonus_system_percent_enable')) {
        $bonusAmount = $this->calculateBonusPercent($count);
        $user->increment('balance', $bonusAmount);
    } else {
        $user->increment('balance', $count);
    }

    // 推荐奖励系统
    if(config('app.referral_system_enabled')) {
        $referral = Referral::where('referred_id', $user_id)->first();
        if ($referral) {
            $referrer = $referral->referrer;
            $referralBonus = intval(round($count * config('app.referral_bonus_percent', 10) / 100));

            if ($referralBonus > 0) {
                $referrer->increment('balance', $referralBonus);

                ReferralBonus::create([
                    'referrer_id' => $referrer->id,
                    'referred_id' => $user->id,
                    'bonus_amount' => $referralBonus,
                    'status' => 'Success'
                ]);
            }
        }
    }

    Log::info("用户充值处理完成: {$user_id}");
}
```

#### 角色充值处理
```php
private function processCharacterPayment($char_id, $count, $selected_server)
{
    // 百分比奖励系统
    if (config('app.bonus_system_percent_enable')) {
        $bonusAmount = $this->calculateBonusPercent($count);
        ModelFactory::l2jModel(config('app.l2server_version'))->addItem($char_id, $bonusAmount, $selected_server);
    } else {
        ModelFactory::l2jModel(config('app.l2server_version'))->addItem($char_id, $count, $selected_server);
    }

    Log::info("角色充值处理完成: {$char_id}");
}
```

---

## 🎁 奖励系统实现

### 百分比奖励计算
```php
private function calculateBonusPercent($count)
{
    $bonusConfig = config('app.bonus_system_percent');

    // 检查最小充值门槛
    if ($count < $bonusConfig['min_coins']) {
        return $count;
    }

    $bonusLevels = $bonusConfig['bonus_levels'];
    ksort($bonusLevels);

    // 确定奖励百分比
    $bonusPercent = 0;
    foreach ($bonusLevels as $threshold => $bonus) {
        if ($count >= $threshold) {
            $bonusPercent = $bonus['bonus_percent'];
        } else {
            break;
        }
    }

    // 计算最终金额
    $bonusAmount = floor($count + ($count * ($bonusPercent / 100)));
    return $bonusAmount;
}
```

### 推荐奖励系统
- 推荐人获得被推荐人充值金额的配置百分比奖励
- 奖励记录保存到 `ReferralBonus` 表
- 支持奖励历史查询和统计

---

## 🗄️ 数据库设计

### 订单表结构 (donate_orders)
```sql
CREATE TABLE `donate_orders` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `payment_id` varchar(255) NOT NULL UNIQUE,
  `user_id` varchar(255) NOT NULL,
  `count` int NOT NULL DEFAULT 0,
  `status` varchar(255) NOT NULL DEFAULT '0',
  `server_id` varchar(255) NULL DEFAULT NULL,
  `pay_system` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
);
```

### 字段说明
- `payment_id`: 唯一支付标识符
- `user_id`: 用户ID或角色ID
- `count`: 充值数量
- `status`: 订单状态 (0=待处理, 1=已完成)
- `server_id`: 服务器ID (未登录用户充值时使用)
- `pay_system`: 支付方式标识

---

## 🔐 安全机制

### 1️⃣ 签名验证
每个支付平台都有独立的签名验证算法，确保回调数据的真实性。

### 2️⃣ 重复处理防护
通过订单状态检查，防止同一订单被重复处理。

### 3️⃣ 数据库事务
所有支付处理都在数据库事务中进行，确保数据一致性。

### 4️⃣ 异常处理
完整的异常捕获和日志记录机制。

---

## ⚙️ 配置管理

### 支付系统配置 (config/app.php)
```php
'pay_system' => [
    'coin_price' => [
        'USD' => 1.00,  // 美元价格
        'EUR' => 0.90,  // 欧元价格
        'RUB' => 75.00, // 卢布价格
    ],

    // FreeKassa配置
    'freekassa_enable' => true,
    'freekassa_currency' => 'RUB',
    'freekassa_project_id' => 'your_project_id',
    'freekassa_secret_key' => 'your_secret_key',
    'freekassa_secret_key_2' => 'your_secret_key_2',

    // PayPal配置
    'paypal_enable' => true,
    'paypal_currency' => 'USD',
    'paypal_mode' => 'sandbox', // sandbox 或 live
    'paypal_sandbox_client_id' => 'your_client_id',
    'paypal_sandbox_client_secret' => 'your_client_secret',

    // 其他支付平台配置...
],
```

---

## 📊 业务流程总结

1. **用户发起充值** → 选择支付方式和金额
2. **系统验证** → 验证用户身份或角色存在性
3. **创建订单** → 生成唯一payment_id，保存订单信息
4. **跳转支付** → 调用对应支付服务，跳转到第三方支付页面
5. **用户支付** → 在第三方平台完成支付
6. **回调处理** → 支付平台回调，验证签名和数据
7. **订单完成** → 更新订单状态，执行充值逻辑
8. **奖励发放** → 根据配置发放百分比奖励和推荐奖励

整个支付系统设计完善，支持多种支付方式、多种充值模式、完整的奖励系统和安全验证机制。

---

## 📝 关键文件清单

### 控制器层
- `app/Http/Controllers/DonateController.php` - 支付控制器

### 服务层
- `app/Services/Payment/PaymentProcessingService.php` - 支付处理核心服务
- `app/Services/Payment/FreeKassaService.php` - FreeKassa支付服务
- `app/Services/Payment/PayPalService.php` - PayPal支付服务
- `app/Services/Payment/StripeService.php` - Stripe支付服务
- `app/Services/Payment/MoruneService.php` - Morune支付服务
- `app/Services/Payment/PrimePaymentsService.php` - PrimePayments支付服务
- `app/Services/Payment/EnotService.php` - Enot支付服务
- `app/Services/Payment/MidTransService.php` - MidTrans支付服务

### 模型层
- `app/Models/DonateOrder.php` - 订单模型
- `app/Models/User.php` - 用户模型
- `app/Models/Referral.php` - 推荐关系模型
- `app/Models/ReferralBonus.php` - 推荐奖励模型

### 配置文件
- `config/app.php` - 支付系统配置
- `config/paypal.php` - PayPal专用配置

### 路由文件
- `routes/web.php` - 支付相关路由

### 数据库迁移
- `database/migrations/2024_01_25_150822_create_donate_orders_table.php` - 订单表迁移

---

*文档创建时间: 2025-08-01*
*版本: v1.0 - 完整业务代码流程分析*
