<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use Exception;

class GameDatabaseComprehensiveTest extends TestCase
{
    /**
     * 综合测试游戏数据库连接和功能
     */
    public function test_comprehensive_game_database_functionality()
    {
        echo "\n" . str_repeat("=", 80);
        echo "\n🎮 游戏世界数据库综合测试开始";
        echo "\n" . str_repeat("=", 80);

        // 1. 基础连接测试
        $this->runConnectionTests();
        
        // 2. 表结构验证
        $this->runTableStructureTests();
        
        // 3. 数据完整性测试
        $this->runDataIntegrityTests();
        
        // 4. 功能性测试
        $this->runFunctionalityTests();
        
        // 5. 性能测试
        $this->runPerformanceTests();

        echo "\n" . str_repeat("=", 80);
        echo "\n✅ 游戏世界数据库综合测试完成";
        echo "\n" . str_repeat("=", 80);
    }

    /**
     * 运行连接测试
     */
    private function runConnectionTests()
    {
        echo "\n\n📡 1. 数据库连接测试";
        echo "\n" . str_repeat("-", 40);

        // 测试 lin2world 连接
        try {
            DB::connection('lin2world')->getPdo();
            $version = DB::connection('lin2world')->select('SELECT @@VERSION as version')[0]->version;
            echo "\n✅ lin2world 连接成功";
            echo "\n   版本: " . substr($version, 0, 50) . "...";
        } catch (Exception $e) {
            echo "\n❌ lin2world 连接失败: " . $e->getMessage();
            $this->fail('lin2world 数据库连接失败');
        }

        // 测试 lin2db 连接
        try {
            DB::connection('lin2db')->getPdo();
            $version = DB::connection('lin2db')->select('SELECT @@VERSION as version')[0]->version;
            echo "\n✅ lin2db 连接成功";
            echo "\n   版本: " . substr($version, 0, 50) . "...";
        } catch (Exception $e) {
            echo "\n❌ lin2db 连接失败: " . $e->getMessage();
            $this->fail('lin2db 数据库连接失败');
        }
    }

    /**
     * 运行表结构测试
     */
    private function runTableStructureTests()
    {
        echo "\n\n🗂️ 2. 表结构验证测试";
        echo "\n" . str_repeat("-", 40);

        // 检查 lin2world 表
        $lin2worldTables = $this->getTableList('lin2world');
        echo "\n📋 lin2world 数据库表 (" . count($lin2worldTables) . " 个):";
        
        $requiredTables = ['user_data', 'clan_data', 'castle'];
        $foundRequired = 0;
        
        foreach ($lin2worldTables as $table) {
            $isRequired = in_array($table, $requiredTables) ? ' ⭐' : '';
            echo "\n   - " . $table . $isRequired;
            if (in_array($table, $requiredTables)) {
                $foundRequired++;
            }
        }
        
        echo "\n✅ 找到 {$foundRequired}/{" . count($requiredTables) . "} 个必需表";

        // 检查 lin2db 表
        $lin2dbTables = $this->getTableList('lin2db');
        echo "\n\n📋 lin2db 数据库表 (" . count($lin2dbTables) . " 个):";
        
        foreach ($lin2dbTables as $table) {
            echo "\n   - " . $table;
        }
    }

    /**
     * 运行数据完整性测试
     */
    private function runDataIntegrityTests()
    {
        echo "\n\n📊 3. 数据完整性测试";
        echo "\n" . str_repeat("-", 40);

        // 检查关键表的数据量
        $this->checkTableDataCount('lin2world', 'user_data', '角色数据');
        $this->checkTableDataCount('lin2world', 'clan_data', '公会数据');
        $this->checkTableDataCount('lin2world', 'castle', '城堡数据');
        $this->checkTableDataCount('lin2db', 'user_account', '用户账户');
    }

    /**
     * 运行功能性测试
     */
    private function runFunctionalityTests()
    {
        echo "\n\n⚙️ 4. 功能性测试";
        echo "\n" . str_repeat("-", 40);

        // 测试排行榜查询
        $this->testRankingQueries();
        
        // 测试统计查询
        $this->testStatisticsQueries();
        
        // 测试在线状态查询
        $this->testOnlineStatusQueries();
    }

    /**
     * 运行性能测试
     */
    private function runPerformanceTests()
    {
        echo "\n\n⚡ 5. 性能测试";
        echo "\n" . str_repeat("-", 40);

        // 测试查询响应时间
        $this->measureQueryPerformance('lin2world', 'SELECT 1', '简单查询');
        $this->measureQueryPerformance('lin2db', 'SELECT 1', '简单查询');
        
        // 测试复杂查询性能
        if ($this->tableExists('lin2world', 'user_data')) {
            $this->measureQueryPerformance('lin2world', 'SELECT COUNT(*) FROM user_data', '计数查询');
        }
    }

    /**
     * 获取表列表
     */
    private function getTableList($connection)
    {
        try {
            $tables = DB::connection($connection)->select("
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_NAME
            ");
            
            return array_map(function($table) {
                return $table->TABLE_NAME;
            }, $tables);
        } catch (Exception $e) {
            echo "\n❌ 获取 {$connection} 表列表失败: " . $e->getMessage();
            return [];
        }
    }

    /**
     * 检查表数据量
     */
    private function checkTableDataCount($connection, $tableName, $description)
    {
        try {
            if ($this->tableExists($connection, $tableName)) {
                $count = DB::connection($connection)->table($tableName)->count();
                echo "\n📈 {$description} ({$tableName}): {$count} 条记录";
            } else {
                echo "\n⚠️ {$description} ({$tableName}): 表不存在";
            }
        } catch (Exception $e) {
            echo "\n❌ {$description} ({$tableName}): 查询失败 - " . $e->getMessage();
        }
    }

    /**
     * 测试排行榜查询
     */
    private function testRankingQueries()
    {
        echo "\n🏆 测试排行榜查询功能...";
        
        if ($this->tableExists('lin2world', 'user_data')) {
            try {
                // 尝试获取角色排行榜
                $characters = DB::connection('lin2world')
                    ->table('user_data')
                    ->limit(3)
                    ->get();
                
                echo "\n   ✅ 角色排行榜查询成功 (获取到 " . count($characters) . " 条记录)";
            } catch (Exception $e) {
                echo "\n   ❌ 角色排行榜查询失败: " . $e->getMessage();
            }
        }
    }

    /**
     * 测试统计查询
     */
    private function testStatisticsQueries()
    {
        echo "\n📊 测试统计查询功能...";
        
        try {
            // 测试基本统计
            if ($this->tableExists('lin2world', 'user_data')) {
                $charCount = DB::connection('lin2world')->table('user_data')->count();
                echo "\n   ✅ 角色统计查询成功: {$charCount} 个角色";
            }
            
            if ($this->tableExists('lin2world', 'clan_data')) {
                $clanCount = DB::connection('lin2world')->table('clan_data')->count();
                echo "\n   ✅ 公会统计查询成功: {$clanCount} 个公会";
            }
            
        } catch (Exception $e) {
            echo "\n   ❌ 统计查询失败: " . $e->getMessage();
        }
    }

    /**
     * 测试在线状态查询
     */
    private function testOnlineStatusQueries()
    {
        echo "\n🌐 测试在线状态查询功能...";
        
        try {
            if ($this->tableExists('lin2db', 'user_account')) {
                $accountCount = DB::connection('lin2db')->table('user_account')->count();
                echo "\n   ✅ 账户状态查询成功: {$accountCount} 个账户";
            }
        } catch (Exception $e) {
            echo "\n   ❌ 在线状态查询失败: " . $e->getMessage();
        }
    }

    /**
     * 测量查询性能
     */
    private function measureQueryPerformance($connection, $query, $description)
    {
        try {
            $startTime = microtime(true);
            DB::connection($connection)->select($query);
            $endTime = microtime(true);
            
            $duration = ($endTime - $startTime) * 1000; // 转换为毫秒
            echo "\n⏱️ {$connection} {$description}: " . round($duration, 2) . " ms";
            
        } catch (Exception $e) {
            echo "\n❌ {$connection} {$description} 性能测试失败: " . $e->getMessage();
        }
    }

    /**
     * 检查表是否存在
     */
    private function tableExists($connection, $tableName)
    {
        try {
            $result = DB::connection($connection)->select("
                SELECT COUNT(*) as count
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = ? AND TABLE_TYPE = 'BASE TABLE'
            ", [$tableName]);
            
            return $result[0]->count > 0;
        } catch (Exception $e) {
            return false;
        }
    }
}
