<?php

/**
 * 游戏功能专项测试脚本
 * 
 * 基于测试结果，验证具体的游戏功能数据
 */

require_once __DIR__ . '/vendor/autoload.php';

// 启动 Laravel 应用
$app = require_once __DIR__ . '/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use Illuminate\Support\Facades\DB;

echo "\n" . str_repeat("=", 80);
echo "\n🎮 游戏功能专项测试";
echo "\n" . str_repeat("=", 80);

/**
 * 测试 PvP 排行榜功能
 */
function testPvPRanking() {
    echo "\n\n🏆 测试 PvP 排行榜功能...";
    
    try {
        // 检查 user_data 表中的 PvP 相关字段
        $columns = DB::connection('lin2world')->select("
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'user_data'
            AND (COLUMN_NAME LIKE '%PK%' OR COLUMN_NAME LIKE '%pvp%' OR COLUMN_NAME LIKE '%Duel%')
        ");
        
        echo "\n📋 PvP 相关字段:";
        foreach ($columns as $column) {
            echo "\n   - " . $column->COLUMN_NAME;
        }
        
        // 测试 PvP 排行榜查询
        $topPvP = DB::connection('lin2world')
            ->table('user_data')
            ->select('char_name', 'PK', 'Duel', 'Lev')
            ->orderBy('PK', 'desc')
            ->limit(10)
            ->get();
        
        echo "\n🏆 PvP 排行榜 (前10名):";
        foreach ($topPvP as $index => $player) {
            $rank = $index + 1;
            echo "\n   {$rank}. {$player->char_name} - PK: {$player->PK}, 决斗: {$player->Duel}, 等级: {$player->Lev}";
        }
        
        // 统计 PvP 数据
        $totalPK = DB::connection('lin2world')->table('user_data')->sum('PK');
        $totalDuel = DB::connection('lin2world')->table('user_data')->sum('Duel');
        $activePvPers = DB::connection('lin2world')->table('user_data')->where('PK', '>', 0)->count();
        
        echo "\n📊 PvP 统计:";
        echo "\n   总 PK 数: {$totalPK}";
        echo "\n   总决斗数: {$totalDuel}";
        echo "\n   有 PK 记录的玩家: {$activePvPers}";
        
    } catch (Exception $e) {
        echo "\n❌ PvP 排行榜测试失败: " . $e->getMessage();
    }
}

/**
 * 测试公会排行榜功能
 */
function testClanRanking() {
    echo "\n\n🏰 测试公会排行榜功能...";
    
    try {
        // 检查是否有 Pledge 表（公会表）
        $pledgeExists = DB::connection('lin2world')->select("
            SELECT COUNT(*) as count
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'Pledge'
        ")[0]->count > 0;
        
        if ($pledgeExists) {
            echo "\n✅ 找到 Pledge 表";
            
            // 获取 Pledge 表结构
            $columns = DB::connection('lin2world')->select("
                SELECT COLUMN_NAME, DATA_TYPE
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'Pledge'
                ORDER BY ORDINAL_POSITION
            ");
            
            echo "\n📋 Pledge 表字段:";
            foreach ($columns as $column) {
                echo "\n   - {$column->COLUMN_NAME} ({$column->DATA_TYPE})";
            }
            
            // 测试公会数据
            $totalClans = DB::connection('lin2world')->table('Pledge')->count();
            echo "\n📊 总公会数: {$totalClans}";
            
            // 获取公会列表
            $clans = DB::connection('lin2world')
                ->table('Pledge')
                ->limit(10)
                ->get();
            
            echo "\n🏰 公会列表 (前10个):";
            foreach ($clans as $index => $clan) {
                $clanName = $clan->clan_name ?? $clan->name ?? '未知';
                $clanLevel = $clan->clan_level ?? $clan->level ?? '未知';
                echo "\n   " . ($index + 1) . ". {$clanName} (等级: {$clanLevel})";
            }
            
            // 统计公会成员
            $clanMembers = DB::connection('lin2world')
                ->table('user_data')
                ->where('pledge_id', '>', 0)
                ->count();
            
            echo "\n👥 公会成员总数: {$clanMembers}";
            
        } else {
            echo "\n❌ 未找到 Pledge 表";
        }
        
    } catch (Exception $e) {
        echo "\n❌ 公会排行榜测试失败: " . $e->getMessage();
    }
}

/**
 * 测试城堡信息功能
 */
function testCastleInfo() {
    echo "\n\n🏯 测试城堡信息功能...";
    
    try {
        // 获取城堡数据
        $castles = DB::connection('lin2world')
            ->table('castle')
            ->get();
        
        echo "\n🏯 城堡信息:";
        foreach ($castles as $castle) {
            $castleId = $castle->id ?? $castle->castle_id ?? '未知';
            $castleName = $castle->name ?? $castle->castle_name ?? "城堡{$castleId}";
            $ownerId = $castle->owner_id ?? $castle->pledge_id ?? 0;
            
            echo "\n   - ID: {$castleId}, 名称: {$castleName}, 拥有者ID: {$ownerId}";
        }
        
        // 检查城堡拥有者
        $ownedCastles = DB::connection('lin2world')
            ->table('castle')
            ->where(function($query) {
                $query->where('owner_id', '>', 0)
                      ->orWhere('pledge_id', '>', 0);
            })
            ->count();
        
        echo "\n📊 已被占领的城堡: {$ownedCastles}";
        
    } catch (Exception $e) {
        echo "\n❌ 城堡信息测试失败: " . $e->getMessage();
    }
}

/**
 * 测试在线统计功能
 */
function testOnlineStats() {
    echo "\n\n🌐 测试在线统计功能...";
    
    try {
        // 检查角色在线状态
        $columns = DB::connection('lin2world')->select("
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'user_data'
            AND (COLUMN_NAME LIKE '%online%' OR COLUMN_NAME LIKE '%login%' OR COLUMN_NAME LIKE '%logout%')
        ");
        
        echo "\n📋 在线状态相关字段:";
        foreach ($columns as $column) {
            echo "\n   - " . $column->COLUMN_NAME;
        }
        
        // 统计最近登录的玩家
        $recentLogins = DB::connection('lin2world')
            ->table('user_data')
            ->whereNotNull('login')
            ->where('login', '>', DB::raw("DATEADD(day, -7, GETDATE())"))
            ->count();
        
        echo "\n📊 最近7天登录的角色: {$recentLogins}";
        
        // 统计账户在线状态
        $totalAccounts = DB::connection('lin2db')->table('user_account')->count();
        $recentAccountLogins = DB::connection('lin2db')
            ->table('user_account')
            ->whereNotNull('last_login')
            ->where('last_login', '>', DB::raw("DATEADD(day, -7, GETDATE())"))
            ->count();
        
        echo "\n📊 总账户数: {$totalAccounts}";
        echo "\n📊 最近7天登录的账户: {$recentAccountLogins}";
        
    } catch (Exception $e) {
        echo "\n❌ 在线统计测试失败: " . $e->getMessage();
    }
}

/**
 * 测试等级排行榜功能
 */
function testLevelRanking() {
    echo "\n\n📈 测试等级排行榜功能...";
    
    try {
        // 等级排行榜
        $topLevels = DB::connection('lin2world')
            ->table('user_data')
            ->select('char_name', 'Lev', 'class', 'Exp')
            ->orderBy('Lev', 'desc')
            ->orderBy('Exp', 'desc')
            ->limit(10)
            ->get();
        
        echo "\n🏆 等级排行榜 (前10名):";
        foreach ($topLevels as $index => $player) {
            $rank = $index + 1;
            echo "\n   {$rank}. {$player->char_name} - 等级: {$player->Lev}, 职业: {$player->class}, 经验: {$player->Exp}";
        }
        
        // 等级分布统计
        $levelStats = DB::connection('lin2world')
            ->table('user_data')
            ->selectRaw('
                COUNT(CASE WHEN Lev >= 80 THEN 1 END) as level_80_plus,
                COUNT(CASE WHEN Lev >= 70 AND Lev < 80 THEN 1 END) as level_70_79,
                COUNT(CASE WHEN Lev >= 60 AND Lev < 70 THEN 1 END) as level_60_69,
                COUNT(CASE WHEN Lev < 60 THEN 1 END) as level_under_60,
                MAX(Lev) as max_level,
                AVG(CAST(Lev as FLOAT)) as avg_level
            ')
            ->first();
        
        echo "\n📊 等级分布统计:";
        echo "\n   80级以上: {$levelStats->level_80_plus}";
        echo "\n   70-79级: {$levelStats->level_70_79}";
        echo "\n   60-69级: {$levelStats->level_60_69}";
        echo "\n   60级以下: {$levelStats->level_under_60}";
        echo "\n   最高等级: {$levelStats->max_level}";
        echo "\n   平均等级: " . round($levelStats->avg_level, 2);
        
    } catch (Exception $e) {
        echo "\n❌ 等级排行榜测试失败: " . $e->getMessage();
    }
}

/**
 * 测试数据库查询性能
 */
function testQueryPerformance() {
    echo "\n\n⚡ 测试查询性能...";
    
    $queries = [
        'PvP排行榜' => "SELECT TOP 10 char_name, PK FROM user_data ORDER BY PK DESC",
        '等级排行榜' => "SELECT TOP 10 char_name, Lev FROM user_data ORDER BY Lev DESC",
        '公会统计' => "SELECT COUNT(*) FROM user_data WHERE pledge_id > 0",
        '在线统计' => "SELECT COUNT(*) FROM user_data WHERE login > DATEADD(day, -1, GETDATE())"
    ];
    
    foreach ($queries as $name => $query) {
        try {
            $startTime = microtime(true);
            DB::connection('lin2world')->select($query);
            $endTime = microtime(true);
            
            $duration = ($endTime - $startTime) * 1000;
            echo "\n⏱️ {$name}: " . round($duration, 2) . " ms";
            
        } catch (Exception $e) {
            echo "\n❌ {$name} 查询失败: " . $e->getMessage();
        }
    }
}

// 运行所有测试
echo "\n🚀 开始功能测试...";

testPvPRanking();
testClanRanking();
testCastleInfo();
testOnlineStats();
testLevelRanking();
testQueryPerformance();

echo "\n\n" . str_repeat("=", 80);
echo "\n✅ 游戏功能测试完成";
echo "\n💡 建议: 根据测试结果，可以在 config/app.php 中启用相应的功能";
echo "\n" . str_repeat("=", 80);
echo "\n";
