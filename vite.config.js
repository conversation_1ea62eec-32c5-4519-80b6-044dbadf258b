import laravel from 'laravel-vite-plugin';
import { defineConfig } from 'vite';

export default defineConfig({
    server: {
        host: 'localhost',
        port: 5173,
        strictPort: true,
        hmr: {
            host: 'localhost',
        },
    },
    plugins: [
        laravel({
            input: [
                'public/template/default/css/lin2web.css',
                'public/template/default/css/public.css',
                'public/template/default/css/auth.css',
                'public/template/default/css/swiper-bundle.min.css',
                'public/template/default/css/nouislider.min.css',
                'public/template/default/js/app.js',
                'public/template/default/js/custom.js',
                'public/template/default/js/swiper-bundle.min.js',
                'public/template/default/js/nouislider.min.js',
            ],
            publicDirectory: 'public',
            buildDirectory: 'build',
            refresh: true,
        }),
    ],
});
