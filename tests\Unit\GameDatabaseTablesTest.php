<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use Exception;

class GameDatabaseTablesTest extends TestCase
{
    /**
     * 需要验证的 lin2world 数据库表
     */
    private $lin2worldTables = [
        'user_data',        // 角色数据 (characters)
        'clan_data',        // 公会数据
        'ally_data',        // 联盟数据
        'castle',           // 城堡数据
        'pledge_ext',       // 公会扩展数据
        'user_item',        // 用户物品
        'user_skill',       // 用户技能
        'user_quest',       // 用户任务
    ];

    /**
     * 需要验证的 lin2db 数据库表
     */
    private $lin2dbTables = [
        'user_account',     // 用户账户
        'user_auth',        // 用户认证
        'user_info',        // 用户信息
    ];

    /**
     * 测试 lin2world 数据库表是否存在
     */
    public function test_lin2world_tables_exist()
    {
        try {
            echo "\n🔍 检查 lin2world 数据库表结构...";
            
            // 获取所有表名
            $tables = DB::connection('lin2world')->select("
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_NAME
            ");
            
            $existingTables = array_map(function($table) {
                return $table->TABLE_NAME;
            }, $tables);

            echo "\n📋 lin2world 数据库中的所有表 (" . count($existingTables) . " 个):";
            foreach ($existingTables as $table) {
                echo "\n   - " . $table;
            }

            // 检查必需的表
            $missingTables = [];
            foreach ($this->lin2worldTables as $requiredTable) {
                if (!in_array($requiredTable, $existingTables)) {
                    $missingTables[] = $requiredTable;
                }
            }

            if (!empty($missingTables)) {
                echo "\n❌ 缺少以下必需的表:";
                foreach ($missingTables as $table) {
                    echo "\n   - " . $table;
                }
            } else {
                echo "\n✅ 所有必需的表都存在";
            }

            // 至少应该有一些基本表存在
            $this->assertGreaterThan(0, count($existingTables), 'lin2world 数据库中没有找到任何表');
            
        } catch (Exception $e) {
            $this->fail('检查 lin2world 表结构失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试 lin2db 数据库表是否存在
     */
    public function test_lin2db_tables_exist()
    {
        try {
            echo "\n🔍 检查 lin2db 数据库表结构...";
            
            // 获取所有表名
            $tables = DB::connection('lin2db')->select("
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
                ORDER BY TABLE_NAME
            ");
            
            $existingTables = array_map(function($table) {
                return $table->TABLE_NAME;
            }, $tables);

            echo "\n📋 lin2db 数据库中的所有表 (" . count($existingTables) . " 个):";
            foreach ($existingTables as $table) {
                echo "\n   - " . $table;
            }

            // 检查必需的表
            $missingTables = [];
            foreach ($this->lin2dbTables as $requiredTable) {
                if (!in_array($requiredTable, $existingTables)) {
                    $missingTables[] = $requiredTable;
                }
            }

            if (!empty($missingTables)) {
                echo "\n❌ 缺少以下必需的表:";
                foreach ($missingTables as $table) {
                    echo "\n   - " . $table;
                }
            } else {
                echo "\n✅ 所有必需的表都存在";
            }

            // 至少应该有一些基本表存在
            $this->assertGreaterThan(0, count($existingTables), 'lin2db 数据库中没有找到任何表');
            
        } catch (Exception $e) {
            $this->fail('检查 lin2db 表结构失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试关键表的数据量
     */
    public function test_key_tables_data_count()
    {
        try {
            echo "\n📊 检查关键表的数据量...";

            // 检查 lin2world 关键表
            $lin2worldCounts = [];
            $availableTables = $this->getAvailableTables('lin2world');
            
            foreach (['user_data', 'clan_data', 'castle'] as $table) {
                if (in_array($table, $availableTables)) {
                    try {
                        $count = DB::connection('lin2world')->table($table)->count();
                        $lin2worldCounts[$table] = $count;
                        echo "\n   lin2world.{$table}: {$count} 条记录";
                    } catch (Exception $e) {
                        echo "\n   lin2world.{$table}: 查询失败 - " . $e->getMessage();
                    }
                } else {
                    echo "\n   lin2world.{$table}: 表不存在";
                }
            }

            // 检查 lin2db 关键表
            $lin2dbCounts = [];
            $availableTables = $this->getAvailableTables('lin2db');
            
            foreach (['user_account'] as $table) {
                if (in_array($table, $availableTables)) {
                    try {
                        $count = DB::connection('lin2db')->table($table)->count();
                        $lin2dbCounts[$table] = $count;
                        echo "\n   lin2db.{$table}: {$count} 条记录";
                    } catch (Exception $e) {
                        echo "\n   lin2db.{$table}: 查询失败 - " . $e->getMessage();
                    }
                } else {
                    echo "\n   lin2db.{$table}: 表不存在";
                }
            }

            // 验证至少有一些数据
            $this->assertTrue(true, '数据量检查完成'); // 这里不强制要求有数据，只是检查

        } catch (Exception $e) {
            $this->fail('检查表数据量失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取数据库中可用的表列表
     */
    private function getAvailableTables($connection)
    {
        try {
            $tables = DB::connection($connection)->select("
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
            ");
            
            return array_map(function($table) {
                return $table->TABLE_NAME;
            }, $tables);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 测试表结构详细信息
     */
    public function test_table_structure_details()
    {
        try {
            echo "\n🔍 检查关键表的结构详情...";

            // 检查 user_data 表结构（如果存在）
            $availableTables = $this->getAvailableTables('lin2world');
            
            if (in_array('user_data', $availableTables)) {
                $columns = DB::connection('lin2world')->select("
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'user_data'
                    ORDER BY ORDINAL_POSITION
                ");

                echo "\n📋 user_data 表结构:";
                foreach ($columns as $column) {
                    echo "\n   - {$column->COLUMN_NAME} ({$column->DATA_TYPE})";
                }
            }

            // 检查 user_account 表结构（如果存在）
            $availableTables = $this->getAvailableTables('lin2db');
            
            if (in_array('user_account', $availableTables)) {
                $columns = DB::connection('lin2db')->select("
                    SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'user_account'
                    ORDER BY ORDINAL_POSITION
                ");

                echo "\n📋 user_account 表结构:";
                foreach ($columns as $column) {
                    echo "\n   - {$column->COLUMN_NAME} ({$column->DATA_TYPE})";
                }
            }

            $this->assertTrue(true, '表结构检查完成');

        } catch (Exception $e) {
            $this->fail('检查表结构详情失败: ' . $e->getMessage());
        }
    }
}
