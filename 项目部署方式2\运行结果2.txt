PS C:\Users\<USER>\Desktop\work\flyXxtt2> php test_game_features.php

================================================================================
🎮 游戏功能专项测试
================================================================================
🚀 开始功能测试...

🏆 测试 PvP 排行榜功能...
📋 PvP 相关字段:
   - PK
   - PKpardon
   - Duel
🏆 PvP 排行榜 (前10名):
   1. 爆爆 - PK: 13, 决斗: 4, 等级: 41
   2. 尧 - PK: 12, 决斗: 8, 等级: 63
   3. 阿斯弗 - PK: 11, 决斗: 5, 等级: 43
   4. 无名的人 - PK: 11, 决斗: 0, 等级: 42
   5. Sunde - PK: 9, 决斗: 2, 等级: 42
   6. 火萤 - PK: 4, 决斗: 4, 等级: 43
   7. 苏柒柒 - PK: 4, 决斗: 1, 等级: 41
   8. YouYou - PK: 3, 决斗: 1, 等级: 76
   9. 牛大大丶 - PK: 2, 决斗: 7, 等级: 65
   10. 佳佳大长腿 - PK: 2, 决斗: 5, 等级: 76
📊 PvP 统计:
   总 PK 数: 92
   总决斗数: 77
   有 PK 记录的玩家: 26

🏰 测试公会排行榜功能...
✅ 找到 Pledge 表
📋 Pledge 表字段:
   - pledge_id (int)
   - ruler_id (int)
   - name (nvarchar)
   - alliance_id (int)
   - challenge_time (int)
   - root_name_value (int)
   - now_war_id (int)
   - oust_time (int)
   - skill_level (smallint)
   - castle_id (int)
   - agit_id (int)
   - rank (int)
   - name_value (int)
   - status (int)
   - private_flag (int)
   - crest_id (int)
   - is_guilty (int)
   - dismiss_reserved_time (int)
   - alliance_withdraw_time (int)
   - alliance_dismiss_time (int)
   - alliance_ousted_time (int)
   - siege_kill (int)
   - siege_death (int)
   - emblem_id (int)
   - fortress_id (int)
   - castle_siege_defence_count (int)
   - world_id (int)
   - load_date (datetime)
   - unload_date (datetime)
📊 总公会数: 7
🏰 公会列表 (前10个):
   1. 随便杀下 (等级: 未知)
   2. 123123 (等级: 未知)
   3. 伽蓝晨曦之光 (等级: 未知)
   4. 锦衣卫 (等级: 未知)
   5. 一个人玩 (等级: 未知)
   6. 包包 (等级: 未知)
   7. 众神会 (等级: 未知)
👥 公会成员总数: 102

🏯 测试城堡信息功能...
🏯 城堡信息:
   - ID: 1, 名称: gludio_castle, 拥有者ID: 0
   - ID: 2, 名称: dion_castle, 拥有者ID: 0
   - ID: 3, 名称: giran_castle, 拥有者ID: 0
   - ID: 4, 名称: oren_castle, 拥有者ID: 0
   - ID: 5, 名称: aden_castle, 拥有者ID: 0
   - ID: 6, 名称: innadrile_castle, 拥有者ID: 0
   - ID: 7, 名称: godad_castle, 拥有者ID: 0
   - ID: 8, 名称: rune_castle, 拥有者ID: 0
   - ID: 9, 名称: schuttgart_castle, 拥有者ID: 0
❌ 城堡信息测试失败: SQLSTATE[42S22]: [Microsoft][ODBC Driver 18 for SQL Server][SQL Server]列名 'owner_id' 无效。 (Connection: lin2world, SQL: select count(*) as aggregate from [castle] where ([owner_id] > 0 or [pledge_id] > 0))

🌐 测试在线统计功能...
📋 在线状态相关字段:
   - login
   - logout
📊 最近7天登录的角色: 339
📊 总账户数: 421
📊 最近7天登录的账户: 128

📈 测试等级排行榜功能...
🏆 等级排行榜 (前10名):
   1. 珈蓝 - 等级: 79, 职业: 95, 经验: 44588515566
   2. 流氓头子 - 等级: 79, 职业: 113, 经验: 44588515566
   3. TT - 等级: 79, 职业: 92, 经验: 44588515566
   4. 晨曦 - 等级: 79, 职业: 102, 经验: 44588515566
   5. 比卡丘 - 等级: 79, 职业: 95, 经验: 44402714146
   6. 佛罗伦萨骑士 - 等级: 79, 职业: 95, 经验: 42938480451
   7. 轻盈 - 等级: 79, 职业: 95, 经验: 38108492216
   8. 大兵 - 等级: 79, 职业: 95, 经验: 36131997468
   9. 佛罗伦萨 - 等级: 79, 职业: 113, 经验: 35363172110
   10. 恭喜 - 等级: 79, 职业: 92, 经验: 34400717705
📊 等级分布统计:
   80级以上: 0
   70-79级: 51
   60-69级: 39
   60级以下: 938
   最高等级: 79
   平均等级: 26.15

⚡ 测试查询性能...
⏱️ PvP排行榜: 66.53 ms
⏱️ 等级排行榜: 63.37 ms
⏱️ 公会统计: 62.73 ms
⏱️ 在线统计: 63.85 ms

================================================================================
✅ 游戏功能测试完成
💡 建议: 根据测试结果，可以在 config/app.php 中启用相应的功能
================================================================================
PS C:\Users\<USER>\Desktop\work\flyXxtt2>
