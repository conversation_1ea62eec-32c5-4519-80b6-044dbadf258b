<?php

echo "=== 简单MySQL连接测试 ===\n\n";

// 测试基本连接（不指定数据库）
echo "1. 测试基本连接（不指定数据库）...\n";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', 'root');
    echo "✅ 基本连接成功!\n";
    
    // 查看所有数据库
    echo "可用的数据库:\n";
    $stmt = $pdo->query("SHOW DATABASES");
    while ($row = $stmt->fetch()) {
        echo "  - " . $row[0] . "\n";
    }
    
} catch(Exception $e) {
    echo "❌ 基本连接失败: " . $e->getMessage() . "\n";
}

echo "\n";

// 测试指定数据库连接
echo "2. 测试指定数据库连接...\n";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306;dbname=lin2web_cms', 'root', 'root');
    echo "✅ 指定数据库连接成功!\n";
    
    // 测试查询
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch();
    echo "用户表记录数: " . $result['count'] . "\n";
    
} catch(Exception $e) {
    echo "❌ 指定数据库连接失败: " . $e->getMessage() . "\n";
}

echo "\n=== 测试完成 ===\n";
