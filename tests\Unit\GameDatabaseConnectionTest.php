<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use Exception;

class GameDatabaseConnectionTest extends TestCase
{
    /**
     * 测试 lin2world 数据库连接
     */
    public function test_lin2world_database_connection()
    {
        try {
            // 测试基本连接
            $pdo = DB::connection('lin2world')->getPdo();
            $this->assertNotNull($pdo, 'lin2world 数据库连接失败');

            // 测试简单查询
            $result = DB::connection('lin2world')->select('SELECT 1 as test');
            $this->assertEquals(1, $result[0]->test, 'lin2world 数据库查询测试失败');

            // 获取数据库版本信息
            $version = DB::connection('lin2world')->select('SELECT @@VERSION as version')[0]->version;
            $this->assertNotEmpty($version, 'lin2world 数据库版本信息获取失败');

            echo "\n✅ lin2world 数据库连接成功";
            echo "\n📊 数据库版本: " . substr($version, 0, 50) . "...";

        } catch (Exception $e) {
            $this->fail('lin2world 数据库连接失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试 lin2db 数据库连接
     */
    public function test_lin2db_database_connection()
    {
        try {
            // 测试基本连接
            $pdo = DB::connection('lin2db')->getPdo();
            $this->assertNotNull($pdo, 'lin2db 数据库连接失败');

            // 测试简单查询
            $result = DB::connection('lin2db')->select('SELECT 1 as test');
            $this->assertEquals(1, $result[0]->test, 'lin2db 数据库查询测试失败');

            // 获取数据库版本信息
            $version = DB::connection('lin2db')->select('SELECT @@VERSION as version')[0]->version;
            $this->assertNotEmpty($version, 'lin2db 数据库版本信息获取失败');

            echo "\n✅ lin2db 数据库连接成功";
            echo "\n📊 数据库版本: " . substr($version, 0, 50) . "...";

        } catch (Exception $e) {
            $this->fail('lin2db 数据库连接失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试数据库配置是否正确
     */
    public function test_database_configuration()
    {
        // 检查 lin2world 配置
        $lin2worldConfig = config('database.connections.lin2world');
        $this->assertNotNull($lin2worldConfig, 'lin2world 数据库配置不存在');
        $this->assertEquals('sqlsrv', $lin2worldConfig['driver'], 'lin2world 驱动配置错误');
        $this->assertNotEmpty($lin2worldConfig['host'], 'lin2world 主机配置为空');
        $this->assertNotEmpty($lin2worldConfig['database'], 'lin2world 数据库名配置为空');

        // 检查 lin2db 配置
        $lin2dbConfig = config('database.connections.lin2db');
        $this->assertNotNull($lin2dbConfig, 'lin2db 数据库配置不存在');
        $this->assertEquals('sqlsrv', $lin2dbConfig['driver'], 'lin2db 驱动配置错误');
        $this->assertNotEmpty($lin2dbConfig['host'], 'lin2db 主机配置为空');
        $this->assertNotEmpty($lin2dbConfig['database'], 'lin2db 数据库名配置为空');

        // 检查服务器类型配置
        $serverType = config('app.l2server_type');
        $this->assertEquals('pts', $serverType, '服务器类型配置错误');

        echo "\n✅ 数据库配置验证通过";
        echo "\n🔧 lin2world 主机: " . $lin2worldConfig['host'];
        echo "\n🔧 lin2db 主机: " . $lin2dbConfig['host'];
        echo "\n🎮 服务器类型: " . $serverType;
    }

    /**
     * 测试数据库连接超时设置
     */
    public function test_database_connection_timeout()
    {
        $startTime = microtime(true);
        
        try {
            // 测试连接超时
            DB::connection('lin2world')->select('SELECT 1');
            $endTime = microtime(true);
            $duration = $endTime - $startTime;
            
            $this->assertLessThan(10, $duration, 'lin2world 数据库连接超时（超过10秒）');
            echo "\n⏱️ lin2world 连接耗时: " . round($duration, 2) . " 秒";
            
        } catch (Exception $e) {
            $endTime = microtime(true);
            $duration = $endTime - $startTime;
            echo "\n❌ lin2world 连接失败，耗时: " . round($duration, 2) . " 秒";
            throw $e;
        }

        $startTime = microtime(true);
        
        try {
            // 测试连接超时
            DB::connection('lin2db')->select('SELECT 1');
            $endTime = microtime(true);
            $duration = $endTime - $startTime;
            
            $this->assertLessThan(10, $duration, 'lin2db 数据库连接超时（超过10秒）');
            echo "\n⏱️ lin2db 连接耗时: " . round($duration, 2) . " 秒";
            
        } catch (Exception $e) {
            $endTime = microtime(true);
            $duration = $endTime - $startTime;
            echo "\n❌ lin2db 连接失败，耗时: " . round($duration, 2) . " 秒";
            throw $e;
        }
    }
}
