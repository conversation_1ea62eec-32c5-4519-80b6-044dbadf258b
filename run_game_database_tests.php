<?php

/**
 * 游戏数据库测试运行脚本
 * 
 * 这个脚本用于快速运行游戏数据库相关的单元测试
 * 使用方法: php run_game_database_tests.php
 */

require_once __DIR__ . '/vendor/autoload.php';

// 设置环境
$_ENV['APP_ENV'] = 'testing';

echo "\n" . str_repeat("=", 80);
echo "\n🎮 游戏世界数据库测试套件";
echo "\n" . str_repeat("=", 80);

echo "\n\n📋 可用的测试选项:";
echo "\n1. 基础连接测试 (GameDatabaseConnectionTest)";
echo "\n2. 表结构测试 (GameDatabaseTablesTest)";
echo "\n3. 数据集成测试 (GameDataIntegrationTest)";
echo "\n4. 综合测试 (GameDatabaseComprehensiveTest)";
echo "\n5. 运行所有测试";

echo "\n\n请选择要运行的测试 (1-5): ";
$choice = trim(fgets(STDIN));

$commands = [
    '1' => 'php artisan test tests/Unit/GameDatabaseConnectionTest.php --verbose',
    '2' => 'php artisan test tests/Unit/GameDatabaseTablesTest.php --verbose',
    '3' => 'php artisan test tests/Unit/GameDataIntegrationTest.php --verbose',
    '4' => 'php artisan test tests/Unit/GameDatabaseComprehensiveTest.php --verbose',
    '5' => 'php artisan test tests/Unit/GameDatabase*Test.php --verbose'
];

if (isset($commands[$choice])) {
    echo "\n🚀 运行测试: " . $commands[$choice];
    echo "\n" . str_repeat("-", 80);
    
    // 执行测试命令
    $output = [];
    $returnCode = 0;
    exec($commands[$choice] . ' 2>&1', $output, $returnCode);
    
    // 显示输出
    foreach ($output as $line) {
        echo "\n" . $line;
    }
    
    echo "\n" . str_repeat("-", 80);
    if ($returnCode === 0) {
        echo "\n✅ 测试执行完成";
    } else {
        echo "\n❌ 测试执行失败 (退出代码: {$returnCode})";
    }
} else {
    echo "\n❌ 无效的选择，请输入 1-5 之间的数字";
}

echo "\n\n💡 提示:";
echo "\n- 确保数据库连接配置正确";
echo "\n- 确保 SQL Server 驱动已安装";
echo "\n- 如果测试失败，请检查 .env 文件中的数据库配置";
echo "\n- 可以查看 storage/logs/laravel.log 获取详细错误信息";

echo "\n\n📚 手动运行测试命令:";
foreach ($commands as $key => $command) {
    $descriptions = [
        '1' => '基础连接测试',
        '2' => '表结构测试', 
        '3' => '数据集成测试',
        '4' => '综合测试',
        '5' => '所有测试'
    ];
    echo "\n{$key}. {$descriptions[$key]}: {$command}";
}

echo "\n\n";
