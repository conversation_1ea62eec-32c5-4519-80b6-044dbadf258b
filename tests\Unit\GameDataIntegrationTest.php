<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Support\Facades\DB;
use Exception;

class GameDataIntegrationTest extends TestCase
{
    /**
     * 测试游戏统计功能所需的数据查询
     */
    public function test_game_statistics_queries()
    {
        try {
            echo "\n🎮 测试游戏统计功能查询...";

            // 测试角色统计查询
            $this->testCharacterStatistics();
            
            // 测试公会统计查询
            $this->testClanStatistics();
            
            // 测试城堡信息查询
            $this->testCastleInformation();
            
            // 测试在线统计查询
            $this->testOnlineStatistics();

        } catch (Exception $e) {
            $this->fail('游戏统计功能测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 测试角色统计查询
     */
    private function testCharacterStatistics()
    {
        try {
            echo "\n👤 测试角色统计查询...";

            // 检查是否有角色表
            $availableTables = $this->getAvailableTables('lin2world');
            
            if (in_array('user_data', $availableTables)) {
                // 测试角色总数查询
                $totalCharacters = DB::connection('lin2world')->table('user_data')->count();
                echo "\n   总角色数: {$totalCharacters}";

                // 测试在线角色查询（如果有在线状态字段）
                $columns = $this->getTableColumns('lin2world', 'user_data');
                if (in_array('online', $columns) || in_array('OnlineStatus', $columns)) {
                    $onlineField = in_array('online', $columns) ? 'online' : 'OnlineStatus';
                    $onlineCharacters = DB::connection('lin2world')
                        ->table('user_data')
                        ->where($onlineField, 1)
                        ->count();
                    echo "\n   在线角色数: {$onlineCharacters}";
                }

                // 测试等级统计（如果有等级字段）
                if (in_array('level', $columns) || in_array('Level', $columns)) {
                    $levelField = in_array('level', $columns) ? 'level' : 'Level';
                    $maxLevel = DB::connection('lin2world')
                        ->table('user_data')
                        ->max($levelField);
                    echo "\n   最高等级: {$maxLevel}";
                }

                // 测试 PvP 排行榜查询（如果有 PvP 字段）
                if (in_array('PvpKills', $columns) || in_array('pvp_kills', $columns)) {
                    $pvpField = in_array('PvpKills', $columns) ? 'PvpKills' : 'pvp_kills';
                    $topPvp = DB::connection('lin2world')
                        ->table('user_data')
                        ->orderBy($pvpField, 'desc')
                        ->limit(5)
                        ->get([$pvpField, 'char_name']);
                    
                    echo "\n   PvP 排行榜前5名:";
                    foreach ($topPvp as $player) {
                        $name = $player->char_name ?? $player->CharName ?? '未知';
                        $pvp = $player->$pvpField;
                        echo "\n     - {$name}: {$pvp} PvP";
                    }
                }

            } else {
                echo "\n   ❌ 未找到角色数据表 (user_data)";
            }

        } catch (Exception $e) {
            echo "\n   ❌ 角色统计查询失败: " . $e->getMessage();
        }
    }

    /**
     * 测试公会统计查询
     */
    private function testClanStatistics()
    {
        try {
            echo "\n🏰 测试公会统计查询...";

            $availableTables = $this->getAvailableTables('lin2world');
            
            if (in_array('clan_data', $availableTables)) {
                // 测试公会总数
                $totalClans = DB::connection('lin2world')->table('clan_data')->count();
                echo "\n   总公会数: {$totalClans}";

                // 测试公会成员数统计
                $columns = $this->getTableColumns('lin2world', 'clan_data');
                if (in_array('clan_level', $columns) || in_array('ClanLevel', $columns)) {
                    $levelField = in_array('clan_level', $columns) ? 'clan_level' : 'ClanLevel';
                    $topClans = DB::connection('lin2world')
                        ->table('clan_data')
                        ->orderBy($levelField, 'desc')
                        ->limit(5)
                        ->get();
                    
                    echo "\n   公会等级排行榜前5名:";
                    foreach ($topClans as $clan) {
                        $name = $clan->clan_name ?? $clan->ClanName ?? '未知';
                        $level = $clan->$levelField;
                        echo "\n     - {$name}: 等级 {$level}";
                    }
                }

            } else {
                echo "\n   ❌ 未找到公会数据表 (clan_data)";
            }

        } catch (Exception $e) {
            echo "\n   ❌ 公会统计查询失败: " . $e->getMessage();
        }
    }

    /**
     * 测试城堡信息查询
     */
    private function testCastleInformation()
    {
        try {
            echo "\n🏯 测试城堡信息查询...";

            $availableTables = $this->getAvailableTables('lin2world');
            
            if (in_array('castle', $availableTables)) {
                // 测试城堡总数
                $totalCastles = DB::connection('lin2world')->table('castle')->count();
                echo "\n   总城堡数: {$totalCastles}";

                // 获取城堡信息
                $castles = DB::connection('lin2world')
                    ->table('castle')
                    ->limit(10)
                    ->get();
                
                echo "\n   城堡列表:";
                foreach ($castles as $castle) {
                    $id = $castle->id ?? $castle->castle_id ?? '未知';
                    $name = $castle->name ?? $castle->castle_name ?? '城堡' . $id;
                    echo "\n     - ID: {$id}, 名称: {$name}";
                }

            } else {
                echo "\n   ❌ 未找到城堡数据表 (castle)";
            }

        } catch (Exception $e) {
            echo "\n   ❌ 城堡信息查询失败: " . $e->getMessage();
        }
    }

    /**
     * 测试在线统计查询
     */
    private function testOnlineStatistics()
    {
        try {
            echo "\n🌐 测试在线统计查询...";

            // 测试账户在线状态
            $availableTables = $this->getAvailableTables('lin2db');
            
            if (in_array('user_account', $availableTables)) {
                $totalAccounts = DB::connection('lin2db')->table('user_account')->count();
                echo "\n   总账户数: {$totalAccounts}";

                // 检查是否有在线状态字段
                $columns = $this->getTableColumns('lin2db', 'user_account');
                if (in_array('online', $columns) || in_array('OnlineStatus', $columns)) {
                    $onlineField = in_array('online', $columns) ? 'online' : 'OnlineStatus';
                    $onlineAccounts = DB::connection('lin2db')
                        ->table('user_account')
                        ->where($onlineField, 1)
                        ->count();
                    echo "\n   在线账户数: {$onlineAccounts}";
                }
            }

        } catch (Exception $e) {
            echo "\n   ❌ 在线统计查询失败: " . $e->getMessage();
        }
    }

    /**
     * 测试数据库性能
     */
    public function test_database_performance()
    {
        try {
            echo "\n⚡ 测试数据库查询性能...";

            // 测试简单查询性能
            $startTime = microtime(true);
            DB::connection('lin2world')->select('SELECT 1');
            $endTime = microtime(true);
            $duration = ($endTime - $startTime) * 1000; // 转换为毫秒
            echo "\n   lin2world 简单查询耗时: " . round($duration, 2) . " ms";

            $startTime = microtime(true);
            DB::connection('lin2db')->select('SELECT 1');
            $endTime = microtime(true);
            $duration = ($endTime - $startTime) * 1000;
            echo "\n   lin2db 简单查询耗时: " . round($duration, 2) . " ms";

            // 测试复杂查询性能（如果表存在）
            $availableTables = $this->getAvailableTables('lin2world');
            if (in_array('user_data', $availableTables)) {
                $startTime = microtime(true);
                DB::connection('lin2world')->table('user_data')->count();
                $endTime = microtime(true);
                $duration = ($endTime - $startTime) * 1000;
                echo "\n   user_data 计数查询耗时: " . round($duration, 2) . " ms";
            }

            $this->assertTrue(true, '性能测试完成');

        } catch (Exception $e) {
            $this->fail('数据库性能测试失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取数据库中可用的表列表
     */
    private function getAvailableTables($connection)
    {
        try {
            $tables = DB::connection($connection)->select("
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
            ");
            
            return array_map(function($table) {
                return $table->TABLE_NAME;
            }, $tables);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * 获取表的列名列表
     */
    private function getTableColumns($connection, $tableName)
    {
        try {
            $columns = DB::connection($connection)->select("
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = ?
                ORDER BY ORDINAL_POSITION
            ", [$tableName]);
            
            return array_map(function($column) {
                return $column->COLUMN_NAME;
            }, $columns);
        } catch (Exception $e) {
            return [];
        }
    }
}
