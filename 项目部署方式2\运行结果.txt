PS C:\Users\<USER>\Desktop\work\flyXxtt2> php test_game_database.php

Warning: The use statement with non-compound name 'Exception' has no effect in C:\Users\<USER>\Desktop\work\flyXxtt2\test_game_database.php on line 18

================================================================================
🎮 游戏世界数据库连接和表结构测试
================================================================================

🚀 开始测试...

📡 测试 lin2world (游戏世界数据库) 连接...
✅ lin2world (游戏世界数据库) PDO 连接成功
✅ lin2world (游戏世界数据库) 查询测试成功: 1
📊 lin2world (游戏世界数据库) 版本: Microsoft SQL Server 2019 (RTM) - 15.0.2000.5 (X64)
        Sep 24...
🔧 lin2world (游戏世界数据库) 配置:
   主机: *************
   端口: 1433
   数据库: lin2world
   用户名: sa

📡 测试 lin2db (用户账户数据库) 连接...
✅ lin2db (用户账户数据库) PDO 连接成功
✅ lin2db (用户账户数据库) 查询测试成功: 1
📊 lin2db (用户账户数据库) 版本: Microsoft SQL Server 2019 (RTM) - 15.0.2000.5 (X64)
        Sep 24...
🔧 lin2db (用户账户数据库) 配置:
   主机: *************
   端口: 1433
   数据库: lin2db
   用户名: sa

🗂️ 获取 lin2world 表结构...
📋 lin2world 数据库表 (241 个):
   - Academy
   - Academy_Member
   - account_ch2
   - AddedServiceList
   - AddedServiceListEvent
   - Agathion
   - agit
   - agit_adena
   - agit_auction
   - agit_bid
   - agit_deco
   - AirShip
   - all_vip_time
   - Alliance
   - AutoPlayShortCut
   - backup_user_name_color
   - banned_accounts
   - banned_guid
   - banned_ip
   - BannedHardwareId
   - block_use_store
   - BlockIP
   - bookmark
   - bossrecord_round
   - bot_report
   - br_account_data
   - br_drop_item_amount
   - br_event_round
   - br_event_score
   - br_event_top_players
   - br_minigame_score
   - br_product_sold_count
   - br_recent_product
   - br_user_event_data
   - br_user_item_agathion_energy
   - branch_setting
   - builder_account
   - CancelServiceList
   - castle ⭐
   - castle_crop
   - castle_tax
   - castle_war
   - changed_name_by_merge
   - char_pet
   - class_by_race
   - class_list
   - control_tower
   - cursed_weapon
   - daily_mission
   - daily_reset_time
   - dbsaving_map
   - dominion
   - dominion_registry
   - dominion_renamed
   - dominion_siege
   - door
   - duel_record
   - EnterWorldLog
   - event_items
   - event_point
   - EventItemFromWeb
   - field_cycle
   - find_invalid_skill
   - fishing_event_record
   - fishing_event_time
   - fortress
   - fortress_facility
   - fortress_siege
   - fortress_siege_registry
   - hardware_stats
   - instantzone
   - instantzone_flag
   - item_amount0_backup
   - item_auction
   - item_auction_bid
   - item_check
   - item_classid_normal
   - item_expiration
   - item_list
   - item_market_price
   - ItemChangeByScript
   - itemData
   - ItemDelivery
   - login_announce
   - lotto_game
   - lotto_items
   - manor_data
   - manor_data_n
   - manor_fix
   - manor_info
   - map
   - mercenary
   - minigame_agit_member
   - minigame_agit_pledge
   - minigame_agit_status
   - monrace
   - monrace_mon
   - monrace_ticket
   - MultiSell_Log
   - nobless_achievements
   - npc_boss
   - NpcDeath
   - npcname
   - object_data
   - offline_shop
   - offline_shop_items
   - olympiad
   - olympiad_match
   - olympiad_match_count
   - olympiad_result
   - online_bonus
   - OnlineStatistic
   - ownthing
   - pet_data
   - PetitionMsg
   - Pledge
   - pledge_announce
   - pledge_contribution
   - Pledge_Crest
   - pledge_draft_user_list
   - pledge_master_transfer
   - pledge_namevalue_log
   - pledge_power
   - pledge_recruit_board
   - pledge_recruit_waiting_list
   - pledge_skill
   - pm_history
   - point
   - post
   - post_additional_friend
   - post_item_admin
   - premium_account
   - quest
   - QuestData
   - residence_guard
   - residence_tax
   - RestoreNpc
   - reward_for_bossrecord
   - second_auth
   - shared_reuse_delays_of_items
   - shortcut_data
   - siege_agit_pledge
   - SkillData
   - ssq_data
   - ssq_data_backup
   - ssq_join_data
   - ssq_top_point_user
   - ssq_user_data
   - staking_adena_players
   - staking_adena_server
   - staking_adena_usdt
   - Stat_RowCountFromTables
   - sub_pledge
   - subpledge_skill
   - tbl_rank
   - tbl_reward
   - team_battle_agit_member
   - team_battle_agit_pledge
   - test_subpledge_skill
   - time_attack_record
   - time_attack_record_test
   - time_data
   - tmp_user_exp
   - tmp_user_subjob_exp
   - transactions
   - user_ActiveSkill
   - user_ban
   - user_beauty
   - user_birthday_history
   - user_blocklist
   - user_bookmark
   - user_bossrecord
   - user_bossrecord_history
   - user_bossrecord_history_interlude
   - user_bossrecord_interlude
   - user_buff_macro
   - user_comment
   - user_daily_quest
   - user_data ⭐
   - user_data_moved
   - user_data_temp
   - user_deleted
   - user_event_time
   - user_friend
   - user_henna
   - user_history
   - user_home_pccafe_point
   - user_inzone
   - user_item
   - user_item_attribute
   - user_item_deleted
   - user_item_duration
   - user_item_ensoul
   - user_item_period
   - user_item_stack
   - user_journal
   - user_last_color
   - user_log
   - user_macro
   - user_macroinfo
   - user_mail
   - user_mail_receiver
   - user_mail_sender
   - user_name_color
   - user_name_reserved
   - user_navit_advent
   - user_newbie
   - user_nobless
   - user_npc_log
   - user_nr_memo
   - user_pccafe_daily_point
   - user_pccafe_point
   - user_pin
   - user_point
   - user_premium_item
   - user_premium_item_log
   - user_premium_item_receive
   - user_product_penalty
   - user_prohibit
   - user_prohibit_word
   - user_punish
   - user_pvppoint_restrain
   - user_quest
   - user_recipe
   - user_rim_point
   - user_service
   - user_skill
   - user_skill_old
   - user_skill_reuse_delay
   - user_slot
   - user_sociality
   - user_subjob
   - user_summon_abnormal
   - user_summon_data
   - user_surrender
   - user_ui
   - user_vip_messages
   - user_vote_system
   - user_warehouse
   - user_welfare
   - war_declare
✅ 找到重要表: castle, user_data

📊 lin2world 重要表数据统计:
📈  (user_data): 1028 条记录
📋 角色数据表 (user_data) 表结构:
   - char_name (nvarchar) NOT NULL
   - char_id (int) NOT NULL
   - account_name (nvarchar) NOT NULL
   - account_id (int) NOT NULL
   - pledge_id (int) NOT NULL DEFAULT: ((0))
   - builder (tinyint) NOT NULL DEFAULT: ((0))
   - gender (tinyint) NOT NULL
   - race (tinyint) NOT NULL
   - class (tinyint) NOT NULL
   - world (smallint) NOT NULL
   - xloc (int) NOT NULL
   - yloc (int) NOT NULL
   - zloc (int) NOT NULL
   - IsInVehicle (smallint) NOT NULL DEFAULT: ((0))
   - HP (float) NOT NULL
   - MP (float) NOT NULL
   - SP (int) NOT NULL DEFAULT: ((0))
   - Exp (bigint) NULL
   - Lev (tinyint) NOT NULL
   - align (int) NOT NULL
   - PK (int) NOT NULL DEFAULT: ((0))
   - PKpardon (int) NOT NULL DEFAULT: ((0))
   - Duel (int) NOT NULL DEFAULT: ((0))
   - create_date (datetime) NOT NULL DEFAULT: (getdate())
   - login (datetime) NULL
   - logout (datetime) NULL
   - quest_flag (binary) NULL DEFAULT: (0x00)
   - nickname (nvarchar) NULL
   - power_flag (binary) NOT NULL DEFAULT: (0x0000000000000000000000000000000000000000000000000000000000000000)
   - pledge_dismiss_time (int) NOT NULL DEFAULT: ((0))
   - pledge_leave_time (int) NOT NULL DEFAULT: ((0))
   - pledge_leave_status (tinyint) NOT NULL DEFAULT: ((0))
   - max_hp (int) NOT NULL DEFAULT: ((0))
   - max_mp (int) NOT NULL DEFAULT: ((0))
   - quest_memo (char) NULL
   - face_index (int) NOT NULL DEFAULT: ((0))
   - hair_shape_index (int) NOT NULL DEFAULT: ((0))
   - hair_color_index (int) NOT NULL DEFAULT: ((0))
   - use_time (int) NOT NULL DEFAULT: ((0))
   - temp_delete_date (smalldatetime) NULL
   - pledge_ousted_time (int) NOT NULL DEFAULT: ((0))
   - pledge_withdraw_time (int) NOT NULL DEFAULT: ((0))
   - surrender_war_id (int) NOT NULL DEFAULT: ((0))
   - drop_exp (bigint) NULL DEFAULT: ((0))
   - subjob_id (int) NULL
   - ssq_dawn_round (int) NULL
   - cp (float) NOT NULL DEFAULT: ((0))
   - max_cp (float) NOT NULL DEFAULT: ((0))
   - subjob0_class (int) NOT NULL DEFAULT: ((-1))
   - subjob1_class (int) NOT NULL DEFAULT: ((-1))
   - subjob2_class (int) NOT NULL DEFAULT: ((-1))
   - subjob3_class (int) NOT NULL DEFAULT: ((-1))
   - pledge_type (int) NOT NULL DEFAULT: ((0))
   - grade_id (int) NOT NULL DEFAULT: ((0))
   - academy_pledge_id (int) NULL
   - item_duration (int) NOT NULL DEFAULT: ((2100000000))
   - tutorial_flag (binary) NULL DEFAULT: ((0))
   - associated_inzone (int) NULL DEFAULT: ((0))
   - bookmark_slot (int) NOT NULL DEFAULT: ((0))
   - associated_inzone_uid (bigint) NULL DEFAULT: ((0))
   - load_date (datetime) NULL
   - unload_date (datetime) NULL
   - name_color (int) NOT NULL DEFAULT: ((0))
   - title_color (int) NOT NULL DEFAULT: ((0))
   - real_ip (nvarchar) NOT NULL DEFAULT: (N'0.0.0.0')
   - play_wo_shield (int) NOT NULL DEFAULT: ((0))
   - invalid_captcha (int) NOT NULL DEFAULT: ((0))
   - guid (nvarchar) NOT NULL DEFAULT: (N'not-set')
   - last_hwid (varchar) NULL DEFAULT: (NULL)
   - hardware_id (nvarchar) NOT NULL DEFAULT: (N'00000000000000000000000000000000')
📈  (castle): 9 条记录

🗂️ 获取 lin2db 表结构...
📋 lin2db 数据库表 (18 个):
   - banancheg
   - block_msg
   - block_reason_code
   - gm_illegal_login
   - item_code
   - server
   - ssn
   - user_account ⭐
   - user_auth ⭐
   - user_block
   - user_count
   - user_data ⭐
   - user_info ⭐
   - user_pay
   - user_stat
   - user_time
   - userno
   - worldstatus
✅ 找到重要表: user_account, user_auth, user_data, user_info

📊 lin2db 重要表数据统计:
📈  (user_account): 421 条记录
📋 用户账户表 (user_account) 表结构:
   - uid (int) NOT NULL
   - account (varchar) NOT NULL
   - pay_stat (smallint) NOT NULL DEFAULT: ((0))
   - login_flag (int) NOT NULL DEFAULT: ((0))
   - warn_flag (int) NOT NULL DEFAULT: ((0))
   - block_flag (int) NOT NULL DEFAULT: ((0))
   - block_flag2 (int) NOT NULL DEFAULT: ((0))
   - last_login (datetime) NULL
   - last_logout (datetime) NULL
   - subscription_flag (int) NOT NULL DEFAULT: ((0))
   - last_game (int) NULL
   - last_world (int) NULL
   - last_ip (varchar) NULL
   - block_end_date (datetime) NULL
   - forbidden_servers (binary) NULL DEFAULT: ((0))
   - ma_id (int) NOT NULL DEFAULT: ((0))

⚡ 性能测试:
⏱️ lin2world 简单查询: 70.08 ms
⏱️ lin2world 计数查询: 70.14 ms
⏱️ lin2db 简单查询: 65.34 ms
⏱️ lin2db 计数查询: 66.14 ms

================================================================================
📋 测试总结:
----------------------------------------
✅ lin2world 数据库连接正常
   表数量: 241
✅ lin2db 数据库连接正常
   表数量: 18

💡 建议:
✅ 数据库连接正常，可以启用游戏统计功能
🔧 下一步: 在 config/app.php 中启用统计功能
   - 'enable_top_pvp' => true
   - 'enable_clans' => true
   - 'enable_castles' => true
   - 'enable_online' => true

================================================================================
🎮 测试完成
================================================================================
PS C:\Users\<USER>\Desktop\work\flyXxtt2>
