# 🎮 游戏世界数据库连接扩展计划

## 📋 概述

本文档详细说明如何在当前的 Windows 开发环境基础上，扩展连接游戏世界数据库，启用完整的游戏统计功能。

## 🎯 目标

- 连接真实的游戏服务器数据库 (lin2world, lin2db)
- 启用主页游戏统计功能 (PvP排行榜、公会排行榜、城堡信息等)
- 实现完整的游戏数据展示功能

## 📊 当前状态

### ✅ 已完成
- [x] Windows 开发环境搭建完成
- [x] Laravel 项目正常运行 (`http://127.0.0.1`)
- [x] MySQL 数据库连接 (CMS数据库: lin2web_cms)
- [x] SQL Server 驱动安装 (sqlsrv, pdo_sqlsrv)
- [x] PHP 扩展配置完整 (intl, gd, pdo_mysql 等)
- [x] 前端资源构建正常 (Vite)

### ⏳ 待完成
- [ ] 游戏服务器数据库连接配置
- [ ] 游戏统计功能启用
- [ ] 数据库表结构验证
- [ ] 游戏数据展示测试

## 🗂️ 数据库架构说明

### 当前数据库配置

```ini
# .env 文件中的配置
# CMS数据库 (MySQL)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=lin2web_cms
DB_USERNAME=root
DB_PASSWORD=root

# 游戏服务器配置 (SQL Server)
GAME_SERVER_ENABLED=true
L2SERVER_TYPE=pts

# PTS服务器配置
PTS_HOST=127.0.0.1
PTS_PORT=1433
PTS_DATABASE=lin2world
PTS_USERNAME=sa
PTS_PASSWORD=
```
数据库连接信息：
    'pts_servers' => [
        'lin2world' => [
            'server_name' => '经典196',
            'host'      => '*************',
            'port'      => '1433',
            'database'  => 'lin2world',
            'username'  => 'sa',
            'password'  => 'fdjhjdfkJFDJ5165JFDJjdfj!@#',
        ],
    ],
    'lin2db' => [
        'server_name' => 'PTS',
        'host'      => '*************',
        'port'      => '1433',
        'database'  => 'lin2db',
        'username'  => 'sa',
        'password'  => 'fdjhjdfkJFDJ5165JFDJjdfj!@#',
    ],
### 数据库连接说明

1. **lin2web_cms** (MySQL) - CMS系统数据库
   - 用户账户、新闻、工单、充值记录等
   - 已正常连接

2. **lin2world** (SQL Server) - 游戏世界数据库
   - 角色数据、公会信息、城堡数据、PvP统计等
   - 需要配置连接

3. **lin2db** (SQL Server) - 游戏账户数据库
   - 游戏登录账户、角色列表等
   - 需要配置连接

## 🔧 实施步骤

### 第一阶段：数据库连接配置

#### 1.1 配置游戏服务器数据库连接

**目标**: 建立与真实游戏服务器的数据库连接

**步骤**:
1. 获取游戏服务器数据库连接信息
2. 更新 `.env` 文件中的数据库配置
3. 测试数据库连接

**配置示例**:
```ini
# 生产环境游戏服务器配置
PTS_HOST=*************
PTS_PORT=1433
PTS_DATABASE=lin2world
PTS_USERNAME=sa
PTS_PASSWORD=fdjhjdfkJFDJ5165JFDJjdfj!@#
```

#### 1.2 验证数据库表结构

**目标**: 确认游戏数据库中存在所需的表结构

**需要验证的表**:
- `characters` - 角色数据
- `clan_data` - 公会数据
- `ally_data` - 联盟数据
- `castle` - 城堡数据
- `pledge_ext` - 公会扩展数据 (如果存在)

**验证命令**:
```sql
-- 检查表是否存在
SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'lin2world' 
AND TABLE_NAME IN ('characters', 'clan_data', 'ally_data', 'castle');
```

### 第二阶段：游戏统计功能启用

#### 2.1 启用统计功能配置

**文件**: `config/app.php`

**修改内容**:
```php
// 统计功能在主页面 开启/关闭
'enable_top_pvp' => true,     // 启用PvP排行榜
'top_pvp_count' => 9,

'enable_top_pk' => true,      // 启用PK排行榜

'enable_clans' => true,       // 启用公会排行榜
'top_clans_count' => 9,

'enable_castles' => true,     // 启用城堡信息
'enable_online' => true,      // 启用在线统计
```

#### 2.2 服务器版本配置验证

**当前配置**: `'l2server_version' => 'vaganth'`

**需要验证**: 确认游戏服务器使用的是 vaganth 版本，或根据实际情况调整

**可选版本**:
- `l2jscripts`
- `lucera2` 
- `vaganth`
- `l2eternity`
- `depmax`
- `smeli`
- `acis`
- `fandc`

### 第三阶段：功能测试与优化

#### 3.1 数据库连接测试

**测试页面**: `http://127.0.0.1/db-test`

**验证项目**:
- [x] MySQL 连接 (lin2web_cms)
- [ ] SQL Server 连接 (lin2world)
- [ ] SQL Server 连接 (lin2db)

#### 3.2 主页功能测试

**测试项目**:
- [ ] PvP 排行榜显示
- [ ] 公会排行榜显示
- [ ] 城堡信息显示
- [ ] 在线玩家统计
- [ ] 新闻系统正常
- [ ] 前端样式正常

#### 3.3 性能优化

**缓存配置**:
```php
'stats_cached' => 600,              // 统计数据缓存10分钟
'server_status_cached_time' => 60,  // 服务器状态缓存1分钟
```

## ⚠️ 注意事项

### 安全考虑

1. **数据库密码安全**
   - 生产环境密码不要提交到版本控制
   - 使用强密码
   - 定期更换密码

2. **网络安全**
   - 确保数据库服务器防火墙配置正确
   - 使用 VPN 或专用网络连接

### 性能考虑

1. **查询优化**
   - 游戏数据库查询可能较慢，建议启用缓存
   - 限制查询结果数量
   - 使用索引优化查询

2. **错误处理**
   - 游戏服务器可能不稳定，需要完善的错误处理
   - 设置合理的超时时间
   - 提供降级方案

## 🚀 实施时间表

### 第1天：数据库连接配置
- [ ] 获取游戏服务器连接信息
- [ ] 配置 .env 文件
- [ ] 测试数据库连接

### 第2天：功能启用与测试
- [ ] 启用统计功能
- [ ] 验证数据库表结构
- [ ] 测试主页功能

### 第3天：优化与部署
- [ ] 性能优化
- [ ] 错误处理完善
- [ ] 文档更新

## 📞 技术支持

如果在实施过程中遇到问题，可以：

1. 检查 Laravel 日志: `storage/logs/laravel.log`
2. 检查 PHP 错误日志
3. 使用数据库测试页面验证连接
4. 查看网络连接状态

## 📚 相关文档

- [Windows开发环境搭建指南](./Windows开发环境搭建指南.md)
- [数据库配置说明](./数据库配置说明.md)
- [故障排除指南](./故障排除指南.md)

---

**创建时间**: 2025-07-31  
**版本**: v1.0  
**状态**: 待实施
